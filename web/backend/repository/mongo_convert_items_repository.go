package repository

import (
	"context"
	"fmt"
	"math"
	"showfer-web/config"
	"showfer-web/models"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoConvertItemsRepository handles convert items operations in MongoDB
type MongoConvertItemsRepository struct {
	convertItemsCollection *mongo.Collection
	bucketsCollection      *mongo.Collection
}

// NewMongoConvertItemsRepository creates a new MongoDB convert items repository
func NewMongoConvertItemsRepository() *MongoConvertItemsRepository {
	db := config.GetMongoDatabase()
	return &MongoConvertItemsRepository{
		convertItemsCollection: db.Collection("convert_items"),
		bucketsCollection:      db.Collection("buckets"),
	}
}

// GetAllBuckets retrieves all available buckets
func (r *MongoConvertItemsRepository) GetAllBuckets(ctx context.Context) ([]models.BucketResponse, error) {
	cursor, err := r.bucketsCollection.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to find buckets: %w", err)
	}
	defer cursor.Close(ctx)

	var buckets []models.Bucket
	if err := cursor.All(ctx, &buckets); err != nil {
		return nil, fmt.Errorf("failed to decode buckets: %w", err)
	}

	// Convert to response format
	var response []models.BucketResponse
	for _, bucket := range buckets {
		response = append(response, models.BucketResponse{
			ID:         bucket.ID.Hex(),
			BucketName: bucket.BucketName,
			Title:      bucket.Title,
		})
	}

	return response, nil
}

// GetConvertItemsByBucket retrieves convert items filtered by bucket with pagination
func (r *MongoConvertItemsRepository) GetConvertItemsByBucket(ctx context.Context, bucketID string, page, limit int) (*models.ConvertItemsListResponse, error) {
	// Convert bucket ID to ObjectID
	objectID, err := primitive.ObjectIDFromHex(bucketID)
	if err != nil {
		return nil, fmt.Errorf("invalid bucket ID: %w", err)
	}

	// Create filter for items that contain the bucket ID
	filter := bson.M{
		"bucket": bson.M{
			"$in": []primitive.ObjectID{objectID},
		},
	}

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Set up options for pagination and sorting
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "updatedAt", Value: -1}}) // Sort by most recent first

	// Get total count
	total, err := r.convertItemsCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %w", err)
	}

	// Find documents
	cursor, err := r.convertItemsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err := cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	// Convert to response format
	var responseItems []models.ConvertItemResponse
	for _, item := range items {
		responseItems = append(responseItems, item.ToResponse())
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &models.ConvertItemsListResponse{
		Items:      responseItems,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// GetConvertItemsByBucketName retrieves convert items filtered by bucket name with pagination
func (r *MongoConvertItemsRepository) GetConvertItemsByBucketName(ctx context.Context, bucketName string, page, limit int) (*models.ConvertItemsListResponse, error) {
	// First, find the bucket by name to get its ID
	var bucket models.Bucket
	err := r.bucketsCollection.FindOne(ctx, bson.M{"bucketName": bucketName}).Decode(&bucket)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("bucket with name '%s' not found", bucketName)
		}
		return nil, fmt.Errorf("failed to find bucket: %w", err)
	}

	// Create filter for items that contain the bucket ID
	filter := bson.M{
		"bucket": bson.M{
			"$in": []primitive.ObjectID{bucket.ID},
		},
	}

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Set up options for pagination and sorting
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "updatedAt", Value: -1}}) // Sort by most recent first

	// Get total count
	total, err := r.convertItemsCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %w", err)
	}

	// Find documents
	cursor, err := r.convertItemsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err := cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	// Convert to response format
	var responseItems []models.ConvertItemResponse
	for _, item := range items {
		responseItems = append(responseItems, item.ToResponse())
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &models.ConvertItemsListResponse{
		Items:      responseItems,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// GetAllConvertItems retrieves all convert items (no bucket filter) with pagination
func (r *MongoConvertItemsRepository) GetAllConvertItems(ctx context.Context, page, limit int) (*models.ConvertItemsListResponse, error) {
	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Set up options for pagination and sorting
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "updatedAt", Value: -1}}) // Sort by most recent first

	// Get total count
	total, err := r.convertItemsCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %w", err)
	}

	// Find documents
	cursor, err := r.convertItemsCollection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err := cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	// Convert to response format
	var responseItems []models.ConvertItemResponse
	for _, item := range items {
		responseItems = append(responseItems, item.ToResponse())
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &models.ConvertItemsListResponse{
		Items:      responseItems,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// GetConvertItemByID retrieves a single convert item by ID
func (r *MongoConvertItemsRepository) GetConvertItemByID(ctx context.Context, itemID string) (*models.ConvertItemResponse, error) {
	objectID, err := primitive.ObjectIDFromHex(itemID)
	if err != nil {
		return nil, fmt.Errorf("invalid item ID: %w", err)
	}

	var item models.ConvertItemMongo
	err = r.convertItemsCollection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&item)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("convert item not found")
		}
		return nil, fmt.Errorf("failed to find convert item: %w", err)
	}

	response := item.ToResponse()
	return &response, nil
}

// SearchConvertItems searches convert items by name, filename, or location
func (r *MongoConvertItemsRepository) SearchConvertItems(ctx context.Context, query string, bucketID string, page, limit int) (*models.ConvertItemsListResponse, error) {
	// Build search filter
	searchFilter := bson.M{
		"$or": []bson.M{
			{"name": bson.M{"$regex": query, "$options": "i"}},
			{"fileName": bson.M{"$regex": query, "$options": "i"}},
			{"location": bson.M{"$regex": query, "$options": "i"}},
			{"description": bson.M{"$regex": query, "$options": "i"}},
		},
	}

	// Add bucket filter if specified
	if bucketID != "" {
		objectID, err := primitive.ObjectIDFromHex(bucketID)
		if err != nil {
			return nil, fmt.Errorf("invalid bucket ID: %w", err)
		}
		searchFilter["bucket"] = bson.M{"$in": []primitive.ObjectID{objectID}}
	}

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Set up options for pagination and sorting
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "updatedAt", Value: -1}})

	// Get total count
	total, err := r.convertItemsCollection.CountDocuments(ctx, searchFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %w", err)
	}

	// Find documents
	cursor, err := r.convertItemsCollection.Find(ctx, searchFilter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err := cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	// Convert to response format
	var responseItems []models.ConvertItemResponse
	for _, item := range items {
		responseItems = append(responseItems, item.ToResponse())
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &models.ConvertItemsListResponse{
		Items:      responseItems,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// GetItemsByBucketAndLocation retrieves convert items filtered by bucket ID and location
func (r *MongoConvertItemsRepository) GetItemsByBucketAndLocation(ctx context.Context, bucketID primitive.ObjectID, location string) ([]models.ConvertItemMongo, error) {
	filter := bson.M{
		"bucket": bson.M{
			"$in": []primitive.ObjectID{bucketID},
		},
		"location": location,
	}

	cursor, err := r.convertItemsCollection.Find(ctx, filter, options.Find().SetSort(bson.M{"updatedAt": -1}))
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err = cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	return items, nil
}

// GetSubfoldersByBucket retrieves subfolders for a specific location within a bucket
func (r *MongoConvertItemsRepository) GetSubfoldersByBucket(ctx context.Context, bucketID primitive.ObjectID, location string) ([]string, error) {
	// Ensure location ends with / for proper matching
	if location != "/" && !strings.HasSuffix(location, "/") {
		location += "/"
	}

	// Create regex pattern to match subfolders
	var filter bson.M
	if location == "/" {
		// For root, match any location that doesn't start with /
		filter = bson.M{
			"bucket": bson.M{
				"$in": []primitive.ObjectID{bucketID},
			},
			"location": bson.M{
				"$regex": "^[^/]",
			},
		}
	} else {
		// For specific location, match items that start with the location
		filter = bson.M{
			"bucket": bson.M{
				"$in": []primitive.ObjectID{bucketID},
			},
			"location": bson.M{
				"$regex": "^" + strings.ReplaceAll(location, "/", "\\/"),
			},
		}
	}

	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id": "$location",
			},
		},
		{
			"$sort": bson.M{
				"_id": 1,
			},
		},
	}

	cursor, err := r.convertItemsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate subfolders: %w", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode subfolders: %w", err)
	}

	var folders []string
	for _, result := range results {
		if loc, ok := result["_id"].(string); ok && loc != "" {
			// Extract subfolder name from location
			if location == "/" {
				// For root, take the first part before /
				parts := strings.Split(strings.TrimPrefix(loc, "/"), "/")
				if len(parts) > 0 && parts[0] != "" {
					folders = append(folders, parts[0])
				}
			} else {
				// For specific location, extract the next level
				if strings.HasPrefix(loc, location) {
					remaining := strings.TrimPrefix(loc, location)
					if remaining != "" {
						parts := strings.Split(remaining, "/")
						if len(parts) > 0 && parts[0] != "" {
							folders = append(folders, parts[0])
						}
					}
				}
			}
		}
	}

	// Remove duplicates
	uniqueFolders := make([]string, 0)
	seen := make(map[string]bool)
	for _, folder := range folders {
		if !seen[folder] {
			seen[folder] = true
			uniqueFolders = append(uniqueFolders, folder)
		}
	}

	return uniqueFolders, nil
}
